-- CreateEnum
CREATE TYPE "public"."MessageRole" AS ENUM ('USER', 'ASSISTANT');

-- CreateTable
CREATE TABLE "public"."Character" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "characterVersion" TEXT NOT NULL DEFAULT 'default',
    "slug" TEXT NOT NULL,
    "creator" TEXT,
    "avatarUrl" TEXT,
    "description" TEXT,
    "personality" TEXT,
    "scenario" TEXT,
    "firstMessage" TEXT,
    "firstMessageExample" TEXT,
    "systemPrompt" TEXT,
    "alternateGreetings" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "creatorNotes" TEXT,
    "spec" TEXT,
    "specVersion" TEXT,
    "characterBook" JSONB,
    "groupOnlyGreetings" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "postHistoryInstructions" TEXT,
    "extensions" JSONB,
    "rawData" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Character_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ChatSession" (
    "id" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "title" TEXT,
    "modelId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChatSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ChatMessage" (
    "id" TEXT NOT NULL,
    "chatSessionId" TEXT NOT NULL,
    "role" "public"."MessageRole" NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ChatMessage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."LLMProvider" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "baseUrl" TEXT,
    "apiKey" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LLMProvider_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."LLMModel" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "promptPricePer1MTokens" DECIMAL(12,6),
    "completionPricePer1MTokens" DECIMAL(12,6),
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LLMModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CharacterBookEntry" (
    "id" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "entryId" INTEGER NOT NULL,
    "name" TEXT,
    "comment" TEXT,
    "content" TEXT NOT NULL,
    "keys" TEXT[],
    "secondaryKeys" TEXT[],
    "position" TEXT,
    "constant" BOOLEAN,
    "selective" BOOLEAN,
    "insertionOrder" INTEGER,
    "enabled" BOOLEAN,
    "extensions" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CharacterBookEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Character_slug_key" ON "public"."Character"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Character_name_characterVersion_key" ON "public"."Character"("name", "characterVersion");

-- CreateIndex
CREATE INDEX "ChatSession_characterId_updatedAt_idx" ON "public"."ChatSession"("characterId", "updatedAt");

-- CreateIndex
CREATE INDEX "ChatMessage_chatSessionId_createdAt_idx" ON "public"."ChatMessage"("chatSessionId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "LLMProvider_name_key" ON "public"."LLMProvider"("name");

-- CreateIndex
CREATE UNIQUE INDEX "LLMModel_providerId_identifier_key" ON "public"."LLMModel"("providerId", "identifier");

-- CreateIndex
CREATE INDEX "CharacterBookEntry_characterId_insertionOrder_idx" ON "public"."CharacterBookEntry"("characterId", "insertionOrder");

-- CreateIndex
CREATE UNIQUE INDEX "CharacterBookEntry_characterId_entryId_key" ON "public"."CharacterBookEntry"("characterId", "entryId");

-- AddForeignKey
ALTER TABLE "public"."ChatSession" ADD CONSTRAINT "ChatSession_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."Character"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ChatSession" ADD CONSTRAINT "ChatSession_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "public"."LLMModel"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ChatMessage" ADD CONSTRAINT "ChatMessage_chatSessionId_fkey" FOREIGN KEY ("chatSessionId") REFERENCES "public"."ChatSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."LLMModel" ADD CONSTRAINT "LLMModel_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "public"."LLMProvider"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CharacterBookEntry" ADD CONSTRAINT "CharacterBookEntry_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."Character"("id") ON DELETE CASCADE ON UPDATE CASCADE;
