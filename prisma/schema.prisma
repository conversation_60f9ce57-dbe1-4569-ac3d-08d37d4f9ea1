// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Character {
  id               String   @id @default(cuid())
  name             String
  characterVersion String   @default("default")
  slug             String   @unique
  ownerId          String?
  creator          String?
  avatarUrl        String?
  description      String?
  personality      String?
  scenario         String?
  firstMessage     String?
  firstMessageExample String?
  systemPrompt     String?
  alternateGreetings String[] @default([])
  tags             String[] @default([])
  creatorNotes     String?
  spec             String?
  specVersion      String?
  characterBook    Json?
  groupOnlyGreetings String[] @default([])
  postHistoryInstructions String?
  extensions       Json?
  rawData          Json
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  chatSessions     ChatSession[]
  bookEntries      CharacterBookEntry[]
  owner            User?      @relation(fields: [ownerId], references: [id], onDelete: SetNull)

  @@unique([name, characterVersion])
}

model ChatSession {
  id          String   @id @default(cuid())
  characterId String
  title       String?
  modelId     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  character Character  @relation(fields: [characterId], references: [id], onDelete: Cascade)
  model     LLMModel?   @relation(fields: [modelId], references: [id])
  messages  ChatMessage[]

  @@index([characterId, updatedAt])
}

model ChatMessage {
  id            String      @id @default(cuid())
  chatSessionId String
  role          MessageRole
  content       String
  createdAt     DateTime    @default(now())

  chatSession ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)

  @@index([chatSessionId, createdAt])
}

enum MessageRole {
  USER
  ASSISTANT
}

model LLMProvider {
  id        String     @id @default(cuid())
  name      String     @unique
  baseUrl   String?
  apiKey    String?
  metadata  Json?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  models    LLMModel[]
}

model LLMModel {
  id                     String     @id @default(cuid())
  providerId             String
  identifier             String
  displayName            String
  description            String?
  promptPricePer1MTokens Decimal?   @db.Decimal(12, 6)
  completionPricePer1MTokens Decimal? @db.Decimal(12, 6)
  currency               String     @default("USD")
  isDefault              Boolean    @default(false)
  createdAt              DateTime   @default(now())
  updatedAt              DateTime   @updatedAt

  provider LLMProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  sessions ChatSession[]

  @@unique([providerId, identifier])
}

model CharacterBookEntry {
  id              String   @id @default(cuid())
  characterId     String
  entryId         Int
  name            String?
  comment         String?
  content         String
  keys            String[]
  secondaryKeys   String[]
  position        String?
  constant        Boolean?
  selective       Boolean?
  insertionOrder  Int?
  enabled         Boolean?
  extensions      Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  character Character @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@unique([characterId, entryId])
  @@index([characterId, insertionOrder])
}

model User {
  id            String       @id @default(cuid())
  name          String?
  email         String?      @unique
  emailVerified DateTime?
  image         String?
  passwordHash  String?
  role          UserRole     @default(USER)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  characters    Character[]
  accounts      Account[]
  sessions      Session[]
}

model Account {
  id                   String  @id @default(cuid())
  userId               String
  type                 String
  provider             String
  providerAccountId    String
  refresh_token        String? @db.Text
  access_token         String? @db.Text
  expires_at           Int?
  token_type           String?
  scope                String?
  id_token             String? @db.Text
  session_state        String?
  refresh_token_expires_in Int?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserRole {
  USER
  ADMIN
}
