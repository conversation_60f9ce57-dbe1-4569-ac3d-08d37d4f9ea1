import type { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";

export type CharacterAccessOptions = {
  slug: string;
  userId: string;
  isAdmin: boolean;
  include?: Prisma.CharacterInclude;
  select?: Prisma.CharacterSelect;
};

export async function authorizeCharacterAccess<T extends CharacterAccessOptions>({
  slug,
  userId,
  isAdmin,
  include,
  select,
}: T) {
  if (isAdmin) {
    return prisma.character.findUnique({
      where: { slug },
      include,
      select,
    });
  }

  return prisma.character.findFirst({
    where: {
      slug,
      ownerId: userId,
    },
    include,
    select,
  });
}
