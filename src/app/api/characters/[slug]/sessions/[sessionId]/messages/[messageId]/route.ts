import { NextResponse } from "next/server";

import { MessageRole } from "@/generated/prisma";
import { authorizeCharacterAccess } from "@/lib/permissions";
import { prisma } from "@/lib/prisma";
import { auth } from "../../../../../../../../../../auth";

type RouteParams = {
  params: Promise<{
    slug: string;
    sessionId: string;
    messageId: string;
  }>;
};

const mapRole = (role: MessageRole): "assistant" | "user" =>
  role === MessageRole.ASSISTANT ? "assistant" : "user";

export async function PATCH(request: Request, { params }: RouteParams) {
  const { slug, sessionId, messageId } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await authorizeCharacterAccess({
    slug,
    userId: userSession.user.id,
    isAdmin: userSession.user.role === "ADMIN",
    select: {
      id: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const chatSession = await prisma.chatSession.findFirst({
    where: {
      id: sessionId,
      characterId: character.id,
    },
    select: {
      id: true,
    },
  });

  if (!chatSession) {
    return NextResponse.json({ error: "Chat session not found." }, { status: 404 });
  }

  const chatMessage = await prisma.chatMessage.findUnique({
    where: { id: messageId },
    select: {
      id: true,
      chatSessionId: true,
      role: true,
    },
  });

  if (!chatMessage || chatMessage.chatSessionId !== sessionId) {
    return NextResponse.json({ error: "Chat message not found." }, { status: 404 });
  }

  if (![MessageRole.USER, MessageRole.ASSISTANT].includes(chatMessage.role)) {
    return NextResponse.json({ error: "Only user or assistant messages can be edited." }, { status: 400 });
  }

  if (!request.headers.get("content-type")?.includes("application/json")) {
    return NextResponse.json({ error: "Invalid request body." }, { status: 400 });
  }

  let content: string | null = null;

  try {
    const body = (await request.json()) as { content?: unknown };
    if (typeof body?.content === "string") {
      content = body.content;
    }
  } catch {
    return NextResponse.json({ error: "Invalid request body." }, { status: 400 });
  }

  if (typeof content !== "string") {
    return NextResponse.json({ error: "Message content is required." }, { status: 400 });
  }

  const trimmed = content.trim();

  if (trimmed.length === 0) {
    return NextResponse.json({ error: "Message cannot be empty." }, { status: 400 });
  }

  try {
    const [updatedMessage, updatedSession] = await prisma.$transaction([
      prisma.chatMessage.update({
        where: { id: chatMessage.id },
        data: {
          content: trimmed,
        },
      }),
      prisma.chatSession.update({
        where: { id: sessionId },
        data: { updatedAt: new Date() },
        select: {
          id: true,
          updatedAt: true,
        },
      }),
    ]);

    return NextResponse.json({
      chatMessage: {
        id: updatedMessage.id,
        role: mapRole(updatedMessage.role),
        content: updatedMessage.content,
        createdAt: updatedMessage.createdAt.toISOString(),
      },
      chatSession: {
        id: updatedSession.id,
        updatedAt: updatedSession.updatedAt.toISOString(),
      },
    });
  } catch (error) {
    console.error("Failed to update chat message", error);
    return NextResponse.json({ error: "Failed to update message." }, { status: 500 });
  }
}
