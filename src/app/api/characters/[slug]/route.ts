import { NextResponse } from "next/server";

import type { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "../../../../../auth";
import { authorizeCharacterAccess } from "@/lib/permissions";

type RouteParams = {
  params: Promise<{
    slug: string;
  }>;
};

const sanitizeStringArray = (value: unknown) => {
  if (value === null) {
    return [];
  }

  if (!Array.isArray(value)) {
    return undefined;
  }

  const list = value
    .map((item) => (typeof item === "string" ? item.trim() : null))
    .filter((item): item is string => Boolean(item && item.length));

  return list.length ? list : [];
};

export async function GET(_: Request, { params }: RouteParams) {
  const { slug } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await authorizeCharacterAccess({
    slug,
    userId: userSession.user.id,
    isAdmin: userSession.user.role === "ADMIN",
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  return NextResponse.json({ character });
}

export async function PATCH(request: Request, { params }: RouteParams) {
  const { slug } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await authorizeCharacterAccess({
    slug,
    userId: userSession.user.id,
    isAdmin: userSession.user.role === "ADMIN",
    select: {
      id: true,
      tags: true,
      alternateGreetings: true,
      groupOnlyGreetings: true,
      ownerId: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const isOwner = character.ownerId === userSession.user.id;
  const isAdmin = userSession.user.role === "ADMIN";

  if (!isOwner && !isAdmin) {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  if (!request.headers.get("content-type")?.includes("application/json")) {
    return NextResponse.json({ error: "Invalid request body." }, { status: 400 });
  }

  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const {
    name,
    description,
    personality,
    scenario,
    creatorNotes,
    firstMessage,
    alternateGreetings,
    groupOnlyGreetings,
    tags,
    avatarUrl,
  } = payload as Record<string, unknown>;

  const updateData: Prisma.CharacterUpdateInput = {};

  if (typeof name === "string" && name.trim().length > 0) {
    updateData.name = name.trim();
  }

  if (typeof avatarUrl !== "undefined") {
    if (avatarUrl === null) {
      updateData.avatarUrl = null;
    } else if (typeof avatarUrl === "string") {
      const trimmed = avatarUrl.trim();
      updateData.avatarUrl = trimmed.length ? trimmed : null;
    } else {
      return NextResponse.json({ error: "avatarUrl must be a string." }, { status: 400 });
    }
  }

  const assignTextField = (
    field: keyof Pick<Prisma.CharacterUpdateInput, "description" | "personality" | "scenario" | "creatorNotes" | "firstMessage">,
    value: unknown,
  ) => {
    if (typeof value === "undefined") {
      return;
    }

    if (value === null) {
      updateData[field] = null;
      return;
    }

    if (typeof value === "string") {
      const trimmed = value.trim();
      updateData[field] = trimmed.length ? trimmed : null;
      return;
    }

    throw new Error(`${String(field)} must be a string or null.`);
  };

  try {
    assignTextField("description", description);
    assignTextField("personality", personality);
    assignTextField("scenario", scenario);
    assignTextField("creatorNotes", creatorNotes);
    assignTextField("firstMessage", firstMessage);
  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    return NextResponse.json({ error: "Invalid payload." }, { status: 400 });
  }

  const alternateGreetingList = sanitizeStringArray(alternateGreetings);
  if (alternateGreetingList !== undefined) {
    updateData.alternateGreetings = alternateGreetingList;
  }

  const groupGreetingList = sanitizeStringArray(groupOnlyGreetings);
  if (groupGreetingList !== undefined) {
    updateData.groupOnlyGreetings = groupGreetingList;
  }

  const tagList = sanitizeStringArray(tags);
  if (tagList !== undefined) {
    updateData.tags = tagList;
  }

  if (Object.keys(updateData).length === 0) {
    return NextResponse.json({ error: "No valid fields to update." }, { status: 400 });
  }

  try {
    const updated = await prisma.character.update({
      where: { id: character.id },
      data: updateData,
      select: {
        id: true,
        slug: true,
        name: true,
        description: true,
        personality: true,
        scenario: true,
        creatorNotes: true,
        firstMessage: true,
        avatarUrl: true,
        alternateGreetings: true,
        groupOnlyGreetings: true,
        tags: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({ character: updated });
  } catch (error) {
    console.error("Failed to update character", error);
    return NextResponse.json({ error: "Failed to update character." }, { status: 500 });
  }
}

export async function DELETE(_: Request, { params }: RouteParams) {
  const { slug } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await authorizeCharacterAccess({
    slug,
    userId: userSession.user.id,
    isAdmin: userSession.user.role === "ADMIN",
    select: {
      id: true,
      ownerId: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const isOwner = character.ownerId === userSession.user.id;
  const isAdmin = userSession.user.role === "ADMIN";

  if (!isOwner && !isAdmin) {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  await prisma.character.delete({
    where: { id: character.id },
  });

  return NextResponse.json({ success: true });
}
